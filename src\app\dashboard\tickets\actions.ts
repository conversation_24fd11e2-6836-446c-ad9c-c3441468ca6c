'use server'

import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { redirect } from 'next/navigation'
import { revalidatePath } from 'next/cache'

// Types
interface TicketWithDetails {
  id: string
  title: string
  description: string
  status: 'open' | 'in_progress' | 'closed'
  priority: 'low' | 'medium' | 'high'
  created_by: string
  assigned_to: string | null
  created_at: string
  updated_at: string
  creator?: {
    id: string
    full_name: string | null
    email: string
    role: string
  }
  assignee?: {
    id: string
    full_name: string | null
    email: string
    role: string
  }
  reply_count?: number
}

interface TicketReply {
  id: string
  ticket_id: string
  user_id: string
  message: string
  created_at: string
  user?: {
    id: string
    full_name: string | null
    email: string
    role: string
  }
}

// Role hierarchy
const ROLE_LEVELS = {
  'system_admin': 1,
  'area_manager': 2,
  'team_manager': 3,
  'sales_employee': 4
} as const

type UserRole = keyof typeof ROLE_LEVELS

// Helper function to create Supabase client
async function createSupabaseClient() {
  const cookieStore = await cookies()

  return createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return cookieStore.getAll()
        },
        setAll(cookiesToSet) {
          try {
            cookiesToSet.forEach(({ name, value, options }) =>
              cookieStore.set(name, value, options)
            )
          } catch {
            // Ignore errors in Server Components
          }
        },
      },
    }
  )
}

// Permission helper functions
async function getUserProfile(supabase: any, userId: string) {
  const { data: profile, error } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', userId)
    .single()

  if (error || !profile) {
    throw new Error('User profile not found')
  }

  return profile
}

function canUserEditTicket(userRole: UserRole, userId: string, ticketCreatorId: string): boolean {
  // System admins can edit all tickets including their own
  if (userRole === 'system_admin') {
    return true
  }

  // Users cannot edit their own tickets (except system admin)
  if (userId === ticketCreatorId) {
    return false
  }

  // Higher roles can edit tickets from lower roles
  return true // This will be enforced by RLS policies
}

function canUserViewTicket(userRole: UserRole, userId: string, ticketCreatorId: string): boolean {
  // This will be enforced by RLS policies, but we can add client-side logic here
  return true
}

// Main ticket functions
export async function createTicket(formData: FormData) {
  const supabase = await createSupabaseClient()

  const {
    data: { user },
  } = await supabase.auth.getUser()

  if (!user) {
    redirect('/login')
  }

  const title = formData.get('title') as string
  const description = formData.get('description') as string
  const priority = formData.get('priority') as string

  if (!title || !description) {
    return { error: 'يرجى ملء جميع الحقول المطلوبة' }
  }

  try {
    const { data, error } = await supabase
      .from('tickets')
      .insert({
        title: title.trim(),
        description: description.trim(),
        priority: priority as 'low' | 'medium' | 'high',
        created_by: user.id,
        status: 'open'
      })
      .select()

    if (error) {
      console.error('Database error:', error)
      return { error: `حدث خطأ في قاعدة البيانات: ${error.message}` }
    }

    if (!data || data.length === 0) {
      return { error: 'لم يتم إنشاء الطلب بنجاح' }
    }

    console.log('Ticket created successfully:', data[0])
    
  } catch (error: any) {
    console.error('Error creating ticket:', error)
    return { error: `حدث خطأ غير متوقع: ${error.message}` }
  }

  redirect('/dashboard/tickets')
}

export async function getTickets(statusFilter: 'all' | 'open' | 'in_progress' | 'closed' | 'open_and_progress' = 'all') {
  try {
    const supabase = await createSupabaseClient()

    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return { data: [], error: 'User not authenticated' }
    }

    // Get user profile
    const userProfile = await getUserProfile(supabase, user.id)

    // Build base query - get tickets first, then enhance with user data
    let query = supabase
      .from('tickets')
      .select('*')
      .order('created_at', { ascending: false })

    // The RLS policies will handle the filtering automatically
    // No need for complex client-side filtering logic

    // RLS policies handle role-based filtering automatically
    // No need for complex client-side filtering

    // Apply status filter
    if (statusFilter !== 'all') {
      if (statusFilter === 'open_and_progress') {
        query = query.in('status', ['open', 'in_progress'])
      } else {
        query = query.eq('status', statusFilter)
      }
    }

    const { data: tickets, error } = await query

    if (error) {
      console.error('Error fetching tickets:', error)
      return { data: [], error: error.message }
    }

    if (!tickets || tickets.length === 0) {
      return { data: [] }
    }

    // Get all unique user IDs
    const userIds = new Set<string>()
    tickets.forEach(ticket => {
      if (ticket.created_by) userIds.add(ticket.created_by)
      if (ticket.assigned_to) userIds.add(ticket.assigned_to)
    })

    // Fetch user data
    const { data: users } = await supabase
      .from('profiles')
      .select('id, full_name, email, role')
      .in('id', Array.from(userIds))

    // Create user lookup map
    const userMap = new Map()
    users?.forEach(user => {
      userMap.set(user.id, user)
    })



    // Get reply counts
    const ticketIds = tickets.map(t => t.id)
    const { data: replyCounts } = await supabase
      .from('ticket_replies')
      .select('ticket_id')
      .in('ticket_id', ticketIds)

    // Create reply count map
    const replyCountMap = new Map()
    replyCounts?.forEach(reply => {
      const count = replyCountMap.get(reply.ticket_id) || 0
      replyCountMap.set(reply.ticket_id, count + 1)
    })

    // Process tickets with user data and reply counts
    const processedTickets = tickets.map(ticket => ({
      ...ticket,
      creator: userMap.get(ticket.created_by) || null,
      assignee: userMap.get(ticket.assigned_to) || null,
      reply_count: replyCountMap.get(ticket.id) || 0
    }))

    return { data: processedTickets }
  } catch (error: any) {
    console.error('Error fetching tickets:', error)
    return { data: [], error: error.message }
  }
}

export async function getTicketDetails(ticketId: string) {
  try {
    const supabase = await createSupabaseClient()

    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return { error: 'غير مصرح لك بالوصول' }
    }

    // Get user profile
    const userProfile = await getUserProfile(supabase, user.id)

    // Get ticket details with joins
    const { data: ticket, error: ticketError } = await supabase
      .from('tickets')
      .select(`
        *,
        creator:profiles!tickets_created_by_fkey(id, full_name, email, role),
        assignee:profiles!tickets_assigned_to_fkey(id, full_name, email, role)
      `)
      .eq('id', ticketId)
      .single()

    if (ticketError || !ticket) {
      return { error: 'الطلب غير موجود أو غير مصرح لك بالوصول إليه' }
    }
    // Get ticket replies with user information
    const { data: replies, error: repliesError } = await supabase
      .from('ticket_replies')
      .select(`
        *,
        user:profiles!ticket_replies_user_id_fkey(id, full_name, email, role)
      `)
      .eq('ticket_id', ticketId)
      .order('created_at', { ascending: true })

    if (repliesError) {
      console.error('Error fetching replies:', repliesError)
      return { error: 'حدث خطأ في تحميل الردود' }
    }

    // Check permissions
    const canEdit = canUserEditTicket(userProfile.role as UserRole, user.id, ticket.created_by)
    const canReply = canUserViewTicket(userProfile.role as UserRole, user.id, ticket.created_by)

    // Check if ticket creator can reply
    const canEmployeeReply = (() => {
      // If user is not the ticket creator, they can reply (subject to other permissions)
      if (user.id !== ticket.created_by) {
        return true
      }

      // If ticket is closed, no one can reply
      if (ticket.status === 'closed') {
        return false
      }

      // Ticket creator can only reply if someone else has replied first
      const hasOtherReply = replies && replies.some(reply => reply.user_id !== ticket.created_by)
      return hasOtherReply
    })()

    return {
      ticket,
      replies: replies || [],
      canEdit,
      canReply,
      canEmployeeReply
    }
  } catch (error: any) {
    console.error('Error fetching ticket details:', error)
    return { error: error.message }
  }
}

export async function addTicketReply(ticketId: string, message: string) {
  try {
    const supabase = await createSupabaseClient()

    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return { error: 'غير مصرح لك بالوصول' }
    }

    if (!message.trim()) {
      return { error: 'يرجى كتابة رسالة' }
    }

    // Get ticket details to check reply permissions
    const { data: ticket } = await supabase
      .from('tickets')
      .select('created_by, status')
      .eq('id', ticketId)
      .single()

    if (!ticket) {
      return { error: 'الطلب غير موجود' }
    }

    // If ticket is closed, no one can reply
    if (ticket.status === 'closed') {
      return { error: 'لا يمكن الرد على الطلبات المغلقة' }
    }

    // If user is the ticket creator, check if someone else has replied first
    if (user.id === ticket.created_by) {
      const { data: existingReplies } = await supabase
        .from('ticket_replies')
        .select('user_id')
        .eq('ticket_id', ticketId)

      const hasOtherReply = existingReplies && existingReplies.some(reply => reply.user_id !== ticket.created_by)

      if (!hasOtherReply) {
        return { error: 'يجب انتظار رد أحد المسؤولين قبل أن تتمكن من الرد' }
      }
    }

    const { data, error } = await supabase
      .from('ticket_replies')
      .insert({
        ticket_id: ticketId,
        user_id: user.id,
        message: message.trim()
      })
      .select()

    if (error) {
      console.error('Error adding reply:', error)
      return { error: 'حدث خطأ في إضافة الرد' }
    }

    revalidatePath('/dashboard/tickets')
    return { success: true }
  } catch (error: any) {
    console.error('Error adding ticket reply:', error)
    return { error: error.message }
  }
}

export async function updateTicketStatus(ticketId: string, status: 'open' | 'in_progress' | 'closed') {
  try {
    const supabase = await createSupabaseClient()

    const {
      data: { user },
    } = await supabase.auth.getUser()

    if (!user) {
      return { error: 'غير مصرح لك بالوصول' }
    }

    const { error } = await supabase
      .from('tickets')
      .update({
        status: status,
        updated_at: new Date().toISOString()
      })
      .eq('id', ticketId)

    if (error) {
      console.error('Error updating ticket status:', error)
      return { error: 'حدث خطأ في تحديث حالة الطلب' }
    }

    revalidatePath('/dashboard/tickets')
    return { success: true }
  } catch (error: any) {
    console.error('Error updating ticket status:', error)
    return { error: error.message }
  }
}


