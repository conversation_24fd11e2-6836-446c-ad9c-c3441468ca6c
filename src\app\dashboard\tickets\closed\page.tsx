import { redirect } from 'next/navigation'
import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { TicketList } from '@/components/tickets/TicketList'

export default async function ClosedTicketsPage() {
  const cookieStore = await cookies()
  
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return cookieStore.getAll()
        },
        setAll(cookiesToSet) {
          try {
            cookiesToSet.forEach(({ name, value, options }) =>
              cookieStore.set(name, value, options)
            )
          } catch {
            // The `setAll` method was called from a Server Component.
            // This can be ignored if you have middleware refreshing
            // user sessions.
          }
        },
      },
    }
  )

  const {
    data: { user },
  } = await supabase.auth.getUser()

  if (!user) {
    redirect('/login')
  }

  const { data: profile } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', user.id)
    .single()

  if (!profile) {
    redirect('/login')
  }

  // All roles can access tickets, but sales employees see only their own tickets
  // Higher roles (team_manager, area_manager, system_admin) see tickets based on their hierarchy

  return (
    <DashboardLayout user={{
      name: profile.full_name || user.email,
      email: profile.email,
      role: profile.role
    }}>
      <TicketList
        userRole={profile.role}
        statusFilter="closed"
        showCreateButton={true}
      />
    </DashboardLayout>
  )
}
