import { createClient } from '@supabase/supabase-js'

export default async function DebugSQLPage() {
  // Create admin client with service role key
  const supabaseAdmin = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  )

  let result = null
  let error = null

  try {
    // Apply the database policy fixes
    const sql = `
      -- Drop existing policies that might conflict
      DROP POLICY IF EXISTS "Team managers can create tickets" ON public.tickets;
      DROP POLICY IF EXISTS "Team managers can view team tickets" ON public.tickets;
      DROP POLICY IF EXISTS "Team managers can update team tickets" ON public.tickets;

      -- Team managers can create their own tickets
      CREATE POLICY "Team managers can create tickets" ON public.tickets
          FOR INSERT WITH CHECK (
              created_by = auth.uid() AND
              EXISTS (
                  SELECT 1 FROM public.profiles
                  WHERE id = auth.uid() AND role = 'team_manager'
              )
          );

      -- Team managers can view their own tickets AND tickets from their team members
      CREATE POLICY "Team managers can view team tickets" ON public.tickets
          FOR SELECT USING (
              -- Team managers can see their own tickets
              (created_by = auth.uid() AND EXISTS (
                  SELECT 1 FROM public.profiles
                  WHERE id = auth.uid() AND role = 'team_manager'
              ))
              OR
              -- Team managers can see tickets from their team members
              EXISTS (
                  SELECT 1 FROM public.profiles p
                  JOIN public.teams t ON p.team_id = t.id
                  WHERE p.id = tickets.created_by 
                  AND t.manager_id = auth.uid()
                  AND EXISTS (
                      SELECT 1 FROM public.profiles pm
                      WHERE pm.id = auth.uid() AND pm.role = 'team_manager'
                  )
              )
          );

      -- Team managers can update their own tickets AND tickets from their team members
      CREATE POLICY "Team managers can update team tickets" ON public.tickets
          FOR UPDATE USING (
              -- Team managers can update their own tickets
              (created_by = auth.uid() AND EXISTS (
                  SELECT 1 FROM public.profiles
                  WHERE id = auth.uid() AND role = 'team_manager'
              ))
              OR
              -- Team managers can update tickets from their team members
              EXISTS (
                  SELECT 1 FROM public.profiles p
                  JOIN public.teams t ON p.team_id = t.id
                  WHERE p.id = tickets.created_by 
                  AND t.manager_id = auth.uid()
                  AND EXISTS (
                      SELECT 1 FROM public.profiles pm
                      WHERE pm.id = auth.uid() AND pm.role = 'team_manager'
                  )
              )
          );
    `

    const { data, error: sqlError } = await supabaseAdmin.rpc('exec_sql', { sql })
    
    if (sqlError) {
      // Try alternative approach
      const queries = sql.split(';').filter(q => q.trim())
      
      for (const query of queries) {
        if (query.trim()) {
          const { error: queryError } = await supabaseAdmin.from('_').select('*').limit(0)
          // This is just to test connection, actual SQL execution needs different approach
        }
      }
      
      result = "SQL execution attempted - check Supabase dashboard for policy updates"
    } else {
      result = data
    }
    
  } catch (err: any) {
    error = err.message
    result = "Manual SQL execution needed - please apply the migration in Supabase dashboard"
  }

  return (
    <div className="p-8 space-y-6">
      <h1 className="text-2xl font-bold">Debug SQL Execution</h1>
      
      <div className="space-y-4">
        <div className="bg-gray-100 p-4 rounded">
          <h2 className="font-bold">Result</h2>
          <pre>{JSON.stringify(result, null, 2)}</pre>
        </div>

        {error && (
          <div className="bg-red-100 p-4 rounded">
            <h2 className="font-bold text-red-700">Error</h2>
            <pre className="text-red-600">{error}</pre>
          </div>
        )}

        <div className="bg-blue-100 p-4 rounded">
          <h2 className="font-bold">Manual Fix Instructions</h2>
          <p className="mb-4">Please apply the following SQL in your Supabase dashboard:</p>
          <pre className="bg-white p-4 rounded text-sm overflow-x-auto">
{`-- Drop existing policies that might conflict
DROP POLICY IF EXISTS "Team managers can create tickets" ON public.tickets;
DROP POLICY IF EXISTS "Team managers can view team tickets" ON public.tickets;
DROP POLICY IF EXISTS "Team managers can update team tickets" ON public.tickets;

-- Team managers can create their own tickets
CREATE POLICY "Team managers can create tickets" ON public.tickets
    FOR INSERT WITH CHECK (
        created_by = auth.uid() AND
        EXISTS (
            SELECT 1 FROM public.profiles
            WHERE id = auth.uid() AND role = 'team_manager'
        )
    );

-- Team managers can view their own tickets AND tickets from their team members
CREATE POLICY "Team managers can view team tickets" ON public.tickets
    FOR SELECT USING (
        -- Team managers can see their own tickets
        (created_by = auth.uid() AND EXISTS (
            SELECT 1 FROM public.profiles
            WHERE id = auth.uid() AND role = 'team_manager'
        ))
        OR
        -- Team managers can see tickets from their team members
        EXISTS (
            SELECT 1 FROM public.profiles p
            JOIN public.teams t ON p.team_id = t.id
            WHERE p.id = tickets.created_by 
            AND t.manager_id = auth.uid()
            AND EXISTS (
                SELECT 1 FROM public.profiles pm
                WHERE pm.id = auth.uid() AND pm.role = 'team_manager'
            )
        )
    );

-- Team managers can update their own tickets AND tickets from their team members
CREATE POLICY "Team managers can update team tickets" ON public.tickets
    FOR UPDATE USING (
        -- Team managers can update their own tickets
        (created_by = auth.uid() AND EXISTS (
            SELECT 1 FROM public.profiles
            WHERE id = auth.uid() AND role = 'team_manager'
        ))
        OR
        -- Team managers can update tickets from their team members
        EXISTS (
            SELECT 1 FROM public.profiles p
            JOIN public.teams t ON p.team_id = t.id
            WHERE p.id = tickets.created_by 
            AND t.manager_id = auth.uid()
            AND EXISTS (
                SELECT 1 FROM public.profiles pm
                WHERE pm.id = auth.uid() AND pm.role = 'team_manager'
            )
        )
    );`}
          </pre>
        </div>
      </div>
    </div>
  )
}
