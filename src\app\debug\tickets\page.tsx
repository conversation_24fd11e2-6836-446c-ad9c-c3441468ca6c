import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'

export default async function DebugTicketsPage() {
  const cookieStore = await cookies()
  
  const supabase = createServerClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return cookieStore.getAll()
        },
        setAll(cookiesToSet) {
          try {
            cookiesToSet.forEach(({ name, value, options }) =>
              cookieStore.set(name, value, options)
            )
          } catch {
            // Ignore
          }
        },
      },
    }
  )

  const { data: { user } } = await supabase.auth.getUser()

  if (!user) {
    return <div>Not authenticated</div>
  }

  // Get user profile
  const { data: profile, error: profileError } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', user.id)
    .single()

  // Get all tickets
  const { data: allTickets, error: ticketsError } = await supabase
    .from('tickets')
    .select('*')
    .order('created_at', { ascending: false })

  // Get all teams
  const { data: allTeams, error: teamsError } = await supabase
    .from('teams')
    .select('*')

  // Get team managed by current user
  const { data: managedTeam, error: managedTeamError } = await supabase
    .from('teams')
    .select('*')
    .eq('manager_id', user.id)
    .single()

  // Get team members if team exists
  let teamMembers = null
  if (managedTeam) {
    const { data: members } = await supabase
      .from('profiles')
      .select('*')
      .eq('team_id', managedTeam.id)
    teamMembers = members
  }

  return (
    <div className="p-8 space-y-6">
      <h1 className="text-2xl font-bold">Debug Tickets Page</h1>
      
      <div className="space-y-4">
        <div className="bg-gray-100 p-4 rounded">
          <h2 className="font-bold">Current User</h2>
          <pre>{JSON.stringify({ id: user.id, email: user.email }, null, 2)}</pre>
        </div>

        <div className="bg-gray-100 p-4 rounded">
          <h2 className="font-bold">User Profile</h2>
          {profileError ? (
            <p className="text-red-500">Error: {profileError.message}</p>
          ) : (
            <pre>{JSON.stringify(profile, null, 2)}</pre>
          )}
        </div>

        <div className="bg-gray-100 p-4 rounded">
          <h2 className="font-bold">Managed Team</h2>
          {managedTeamError ? (
            <p className="text-red-500">Error: {managedTeamError.message}</p>
          ) : managedTeam ? (
            <pre>{JSON.stringify(managedTeam, null, 2)}</pre>
          ) : (
            <p>No team managed by this user</p>
          )}
        </div>

        <div className="bg-gray-100 p-4 rounded">
          <h2 className="font-bold">Team Members</h2>
          {teamMembers ? (
            <pre>{JSON.stringify(teamMembers, null, 2)}</pre>
          ) : (
            <p>No team members found</p>
          )}
        </div>

        <div className="bg-gray-100 p-4 rounded">
          <h2 className="font-bold">All Teams</h2>
          {teamsError ? (
            <p className="text-red-500">Error: {teamsError.message}</p>
          ) : (
            <pre>{JSON.stringify(allTeams, null, 2)}</pre>
          )}
        </div>

        <div className="bg-gray-100 p-4 rounded">
          <h2 className="font-bold">All Tickets ({allTickets?.length || 0})</h2>
          {ticketsError ? (
            <p className="text-red-500">Error: {ticketsError.message}</p>
          ) : (
            <pre>{JSON.stringify(allTickets, null, 2)}</pre>
          )}
        </div>

        <div className="bg-blue-100 p-4 rounded">
          <h2 className="font-bold">Create Test Ticket</h2>
          <form action="/debug/tickets/create" method="post">
            <button type="submit" className="bg-blue-500 text-white px-4 py-2 rounded">
              Create Test Ticket
            </button>
          </form>
        </div>
      </div>
    </div>
  )
}
