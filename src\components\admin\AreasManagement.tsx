'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Plus, Edit, Trash2, MapPin, Users, User } from 'lucide-react'
import { UserRole } from '@/lib/supabase'
import { getRoleArabicName } from '@/lib/roles'
import { toast } from '@/hooks/use-toast'
import { useConfirmDialog } from '@/hooks/use-confirm-dialog'

type Area = {
  id: string
  name: string
  description: string | null
  manager_id: string | null
  created_at: string
  updated_at: string
  manager?: {
    id: string
    full_name: string
    email: string
  } | null
  teams?: {
    id: string
    name: string
    manager_id: string | null
  }[]
}

type Profile = {
  id: string
  full_name: string | null
  email: string
  role: UserRole
}

export function AreasManagement() {
  const [areas, setAreas] = useState<Area[]>([])
  const [availableManagers, setAvailableManagers] = useState<Profile[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')

  const { confirm, ConfirmDialog } = useConfirmDialog()
  
  // Dialog states
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [editingArea, setEditingArea] = useState<Area | null>(null)
  
  // Form states
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    manager_id: ''
  })
  const [formLoading, setFormLoading] = useState(false)

  useEffect(() => {
    fetchAreas()
    fetchAvailableManagers()
  }, [])

  const fetchAreas = async () => {
    try {
      const response = await fetch('/api/admin/areas')
      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'حدث خطأ في جلب المناطق')
      }

      setAreas(data.areas || [])
    } catch (err: any) {
      toast.error(err.message)
    } finally {
      setLoading(false)
    }
  }

  const fetchAvailableManagers = async () => {
    try {
      const response = await fetch('/api/admin/users')
      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'حدث خطأ في جلب المستخدمين')
      }

      // Filter users who can be area managers
      const managers = data.users?.filter((user: Profile) => 
        ['system_admin', 'area_manager'].includes(user.role)
      ) || []

      setAvailableManagers(managers)
    } catch (err: any) {
      console.error('Error fetching managers:', err)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.name.trim()) {
      toast.error('اسم المنطقة مطلوب')
      return
    }

    setFormLoading(true)

    try {
      const url = editingArea ? `/api/admin/areas/${editingArea.id}` : '/api/admin/areas'
      const method = editingArea ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: formData.name.trim(),
          description: formData.description.trim() || null,
          manager_id: formData.manager_id || null
        })
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'حدث خطأ في حفظ المنطقة')
      }

      toast.success(editingArea ? 'تم تحديث المنطقة بنجاح' : 'تم إنشاء المنطقة بنجاح')

      // Reset form and close dialog
      setFormData({ name: '', description: '', manager_id: '' })
      setIsAddDialogOpen(false)
      setIsEditDialogOpen(false)
      setEditingArea(null)

      // Refresh areas list
      fetchAreas()

    } catch (err: any) {
      toast.error(err.message)
    } finally {
      setFormLoading(false)
    }
  }

  const handleEdit = (area: Area) => {
    setEditingArea(area)
    setFormData({
      name: area.name,
      description: area.description || '',
      manager_id: area.manager_id || ''
    })
    setIsEditDialogOpen(true)
  }

  const handleDelete = async (area: Area) => {
    const confirmed = await confirm(
      `هل أنت متأكد من حذف المنطقة "${area.name}"؟`,
      {
        title: 'تأكيد الحذف',
        confirmText: 'حذف',
        cancelText: 'إلغاء',
        variant: 'destructive'
      }
    )

    if (!confirmed) return

    try {
      const response = await fetch(`/api/admin/areas/${area.id}`, {
        method: 'DELETE'
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'حدث خطأ في حذف المنطقة')
      }

      toast.success('تم حذف المنطقة بنجاح')
      fetchAreas()

    } catch (err: any) {
      toast.error(err.message)
    }
  }

  const resetForm = () => {
    setFormData({ name: '', description: '', manager_id: '' })
    setEditingArea(null)
    setError('')
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-muted-foreground">جاري تحميل المناطق...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6" dir="rtl">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">إدارة المناطق</h2>
          <p className="text-muted-foreground">إنشاء وإدارة المناطق الجغرافية</p>
        </div>
        
        <Dialog open={isAddDialogOpen} onOpenChange={(open) => {
          setIsAddDialogOpen(open)
          if (!open) resetForm()
        }}>
          <DialogTrigger asChild>
            <Button className="cursor-pointer">
              <Plus className="h-4 w-4 ml-2" />
              إضافة منطقة
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[500px] text-right" dir="rtl">
            <DialogHeader className="text-right">
              <DialogTitle>إضافة منطقة جديدة</DialogTitle>
              <DialogDescription>
                أدخل بيانات المنطقة الجديدة
              </DialogDescription>
            </DialogHeader>
            
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid gap-2">
                <Label htmlFor="name">اسم المنطقة *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="أدخل اسم المنطقة"
                  className="text-right"
                  dir="rtl"
                  required
                />
              </div>

              <div className="grid gap-2">
                <Label htmlFor="description">الوصف</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="أدخل وصف المنطقة"
                  className="text-right"
                  dir="rtl"
                  rows={3}
                />
              </div>

              <div className="grid gap-2">
                <Label htmlFor="manager">مدير المنطقة</Label>
                <Select value={formData.manager_id || "none"} onValueChange={(value) =>
                  setFormData(prev => ({ ...prev, manager_id: value === "none" ? "" : value }))
                }>
                  <SelectTrigger className="text-right" dir="rtl">
                    <SelectValue placeholder="اختر مدير المنطقة" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">بدون مدير</SelectItem>
                    {availableManagers.map((manager) => (
                      <SelectItem key={manager.id} value={manager.id}>
                        <div className="flex items-center gap-2 text-right">
                          <span>{manager.full_name || manager.email}</span>
                          <Badge variant="outline" className="text-xs">
                            {getRoleArabicName(manager.role)}
                          </Badge>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>



              <div className="flex gap-2 justify-start">
                <Button type="submit" disabled={formLoading} className="cursor-pointer">
                  {formLoading ? 'جاري الحفظ...' : 'حفظ'}
                </Button>
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => setIsAddDialogOpen(false)}
                  className="cursor-pointer"
                >
                  إلغاء
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>



      {/* Areas Grid */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {areas.map((area) => (
          <Card key={area.id} className="text-right">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleEdit(area)}
                    className="cursor-pointer"
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDelete(area)}
                    className="cursor-pointer text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
                <MapPin className="h-5 w-5 text-muted-foreground" />
              </div>
              <CardTitle className="text-lg">{area.name}</CardTitle>
              {area.description && (
                <CardDescription className="text-sm">
                  {area.description}
                </CardDescription>
              )}
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {/* Manager */}
                <div className="flex items-center gap-2 text-sm">
                  <User className="h-4 w-4" />
                  <span>
                    المدير: {area.manager?.full_name || 'غير محدد'}
                  </span>
                </div>

                {/* Teams Count */}
                <div className="flex items-center gap-2 text-sm">
                  <Users className="h-4 w-4" />
                  <span>
                    الفرق: {area.teams?.length || 0}
                  </span>
                </div>

                {/* Created Date */}
                <div className="text-xs text-muted-foreground">
                  تم الإنشاء: {new Date(area.created_at).toLocaleDateString('ar-SA')}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {areas.length === 0 && (
        <Card>
          <CardContent className="text-center py-8">
            <MapPin className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">لا توجد مناطق</h3>
            <p className="text-muted-foreground mb-4">
              ابدأ بإنشاء منطقة جديدة لتنظيم الفرق والمستخدمين
            </p>
            <Button onClick={() => setIsAddDialogOpen(true)} className="cursor-pointer">
              <Plus className="h-4 w-4 ml-2" />
              إضافة منطقة
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={(open) => {
        setIsEditDialogOpen(open)
        if (!open) resetForm()
      }}>
        <DialogContent className="sm:max-w-[500px] text-right" dir="rtl">
          <DialogHeader className="text-right">
            <DialogTitle>تعديل المنطقة</DialogTitle>
            <DialogDescription>
              تعديل بيانات المنطقة
            </DialogDescription>
          </DialogHeader>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid gap-2">
              <Label htmlFor="edit-name">اسم المنطقة *</Label>
              <Input
                id="edit-name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="أدخل اسم المنطقة"
                className="text-right"
                dir="rtl"
                required
              />
            </div>

            <div className="grid gap-2">
              <Label htmlFor="edit-description">الوصف</Label>
              <Textarea
                id="edit-description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="أدخل وصف المنطقة"
                className="text-right"
                dir="rtl"
                rows={3}
              />
            </div>

            <div className="grid gap-2">
              <Label htmlFor="edit-manager">مدير المنطقة</Label>
              <Select value={formData.manager_id || "none"} onValueChange={(value) =>
                setFormData(prev => ({ ...prev, manager_id: value === "none" ? "" : value }))
              }>
                <SelectTrigger className="text-right" dir="rtl">
                  <SelectValue placeholder="اختر مدير المنطقة" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">بدون مدير</SelectItem>
                  {availableManagers.map((manager) => (
                    <SelectItem key={manager.id} value={manager.id}>
                      <div className="flex items-center gap-2 text-right">
                        <span>{manager.full_name || manager.email}</span>
                        <Badge variant="outline" className="text-xs">
                          {getRoleArabicName(manager.role)}
                        </Badge>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>



            <div className="flex gap-2 justify-start">
              <Button type="submit" disabled={formLoading} className="cursor-pointer">
                {formLoading ? 'جاري الحفظ...' : 'حفظ التغييرات'}
              </Button>
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => setIsEditDialogOpen(false)}
                className="cursor-pointer"
              >
                إلغاء
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      {/* Confirmation Dialog */}
      <ConfirmDialog />
    </div>
  )
}
