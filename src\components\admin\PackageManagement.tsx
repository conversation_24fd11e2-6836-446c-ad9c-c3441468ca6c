'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'

import { Switch } from '@/components/ui/switch'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog'
import { Plus, Edit, Trash2, ShoppingCart } from 'lucide-react'
import { createClient } from '@/lib/supabase/client'
import type { Database } from '@/lib/supabase'
import { toast } from '@/hooks/use-toast'
import { useConfirmDialog } from '@/hooks/use-confirm-dialog'

type Package = Database['public']['Tables']['packages']['Row']

export function PackageManagement() {
  const [packages, setPackages] = useState<Package[]>([])
  const [loading, setLoading] = useState(true)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [editingPackage, setEditingPackage] = useState<Package | null>(null)
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    price: 0,
    is_active: true
  })
  const [error, setError] = useState('')
  const [submitting, setSubmitting] = useState(false)

  const supabase = createClient()
  const { confirm, ConfirmDialog } = useConfirmDialog()

  useEffect(() => {
    fetchPackages()
  }, [])

  const fetchPackages = async () => {
    try {
      const { data, error } = await supabase
        .from('packages')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) throw error
      setPackages(data || [])
    } catch (error) {
      console.error('Error fetching packages:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setSubmitting(true)
    setError('')

    try {
      if (editingPackage) {
        // Update existing package
        const { error } = await supabase
          .from('packages')
          .update({
            name: formData.name,
            description: formData.description,
            price: formData.price,
            is_active: formData.is_active,
            updated_at: new Date().toISOString()
          })
          .eq('id', editingPackage.id)

        if (error) throw error
      } else {
        // Create new package
        const { error } = await supabase
          .from('packages')
          .insert({
            name: formData.name,
            description: formData.description,
            price: formData.price,
            is_active: formData.is_active
          })

        if (error) throw error
      }

      setIsDialogOpen(false)
      resetForm()
      fetchPackages()
    } catch (error: any) {
      console.error('Error saving package:', error)
      setError('حدث خطأ في حفظ الباقة')
    } finally {
      setSubmitting(false)
    }
  }

  const handleEdit = (pkg: Package) => {
    setEditingPackage(pkg)
    setFormData({
      name: pkg.name,
      description: pkg.description || '',
      price: pkg.price,
      is_active: pkg.is_active
    })
    setIsDialogOpen(true)
  }

  const handleDelete = async (pkg: Package) => {
    const confirmed = await confirm(
      `هل أنت متأكد من حذف الباقة "${pkg.name}"؟`,
      {
        title: 'تأكيد الحذف',
        confirmText: 'حذف',
        cancelText: 'إلغاء',
        variant: 'destructive'
      }
    )

    if (!confirmed) return

    try {
      const { error } = await supabase
        .from('packages')
        .delete()
        .eq('id', pkg.id)

      if (error) throw error
      toast.success('تم حذف الباقة بنجاح')
      fetchPackages()
    } catch (error: any) {
      console.error('Error deleting package:', error)
      toast.error('حدث خطأ في حذف الباقة')
    }
  }

  const toggleStatus = async (pkg: Package) => {
    try {
      // Optimistically update the local state first
      setPackages(prevPackages => 
        prevPackages.map(p => 
          p.id === pkg.id 
            ? { ...p, is_active: !p.is_active, updated_at: new Date().toISOString() }
            : p
        )
      )

      const { error } = await supabase
        .from('packages')
        .update({
          is_active: !pkg.is_active,
          updated_at: new Date().toISOString()
        })
        .eq('id', pkg.id)

      if (error) {
        // Revert the optimistic update on error
        setPackages(prevPackages => 
          prevPackages.map(p => 
            p.id === pkg.id 
              ? { ...p, is_active: pkg.is_active }
              : p
          )
        )
        throw error
      }
    } catch (error: any) {
      console.error('Error updating package status:', error)
      toast.error('حدث خطأ في تحديث حالة الباقة')
    }
  }

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      price: 0,
      is_active: true
    })
    setEditingPackage(null)
    setError('')
  }

  const openAddDialog = () => {
    resetForm()
    setIsDialogOpen(true)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>جاري التحميل...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6" dir="rtl">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <ShoppingCart className="h-5 w-5" />
                الباقات المتاحة
              </CardTitle>
              <CardDescription>
                إدارة باقات الخدمات المتاحة للمبيعات
              </CardDescription>
            </div>
            <Button onClick={openAddDialog}>
              <Plus className="h-4 w-4 ml-1" />
              إضافة باقة جديدة
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {packages.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              لا توجد باقات مضافة بعد
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="text-right">اسم الباقة</TableHead>
                    <TableHead className="text-right">الوصف</TableHead>
                    <TableHead className="text-right">السعر</TableHead>
                    <TableHead className="text-center">الحالة</TableHead>
                    <TableHead className="text-center">الإجراءات</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {packages.map((pkg) => (
                    <TableRow key={pkg.id}>
                      <TableCell className="font-medium text-right">{pkg.name}</TableCell>
                      <TableCell className="text-right">{pkg.description || '-'}</TableCell>
                      <TableCell className="text-right font-arabic tabular-nums">{pkg.price.toFixed(2)} ر.س</TableCell>
                      <TableCell className="text-center">
                        <div className="flex items-center justify-center gap-3">
                          <Switch
                            checked={pkg.is_active}
                            onCheckedChange={() => toggleStatus(pkg)}
                            aria-label={pkg.is_active ? 'إلغاء تفعيل الباقة' : 'تفعيل الباقة'}
                          />
                          <span className={`text-sm font-medium min-w-[60px] ${pkg.is_active ? 'text-green-600' : 'text-gray-500'}`}>
                            {pkg.is_active ? 'نشطة' : 'غير نشطة'}
                          </span>
                        </div>
                      </TableCell>
                      <TableCell className="text-center">
                        <div className="flex items-center justify-center gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEdit(pkg)}
                            className="h-8 w-8 p-0"
                          >
                            <Edit className="h-4 w-4" />
                            <span className="sr-only">تعديل الباقة</span>
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDelete(pkg)}
                            className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                          >
                            <Trash2 className="h-4 w-4" />
                            <span className="sr-only">حذف الباقة</span>
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Add/Edit Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-[425px]" dir="rtl">
          <DialogHeader>
            <DialogTitle>
              {editingPackage ? 'تعديل الباقة' : 'إضافة باقة جديدة'}
            </DialogTitle>
            <DialogDescription>
              {editingPackage ? 'قم بتعديل بيانات الباقة' : 'أضف باقة خدمات جديدة للنظام'}
            </DialogDescription>
          </DialogHeader>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">اسم الباقة</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                placeholder="أدخل اسم الباقة"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">الوصف (اختياري)</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                placeholder="أدخل وصف الباقة"
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="price">السعر (ر.س)</Label>
              <Input
                id="price"
                type="number"
                step="0.01"
                min="0"
                value={formData.price}
                onChange={(e) => setFormData({ ...formData, price: parseFloat(e.target.value) || 0 })}
                placeholder="0.00"
                required
              />
            </div>

            {error && (
              <div className="text-sm text-destructive bg-destructive/10 p-3 rounded-md">
                {error}
              </div>
            )}

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsDialogOpen(false)}
              >
                إلغاء
              </Button>
              <Button type="submit" disabled={submitting}>
                {submitting ? 'جاري الحفظ...' : (editingPackage ? 'تحديث' : 'إضافة')}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>



      {/* Confirmation Dialog */}
      <ConfirmDialog />
    </div>
  )
}
