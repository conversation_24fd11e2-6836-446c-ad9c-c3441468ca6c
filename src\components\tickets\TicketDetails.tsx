'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON> } from 'next/navigation'
import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Separator } from '@/components/ui/separator'
import { ArrowRight, Send, User, Clock, CheckCircle, PlayCircle, AlertCircle, Loader2 } from 'lucide-react'
import { TicketStatusBadge } from './TicketStatusBadge'
import { TicketPriorityBadge } from './TicketPriorityBadge'
import { UserAvatar } from '@/components/ui/user-avatar'
import { formatDistanceToNow } from 'date-fns'
import { ar } from 'date-fns/locale'
import { getTicketDetails, addTicketReply, updateTicketStatus } from '@/app/dashboard/tickets/actions'
import { toast } from '@/hooks/use-toast'

interface Ticket {
  id: string
  title: string
  description: string
  status: 'open' | 'in_progress' | 'closed'
  priority: 'low' | 'medium' | 'high'
  created_by: string
  assigned_to: string | null
  created_at: string
  updated_at: string
  creator?: {
    id: string
    full_name: string | null
    email: string
    role: string
  }
  assignee?: {
    id: string
    full_name: string | null
    email: string
    role: string
  }
}

interface TicketReply {
  id: string
  ticket_id: string
  user_id: string
  message: string
  created_at: string
  user?: {
    id: string
    full_name: string | null
    email: string
    role: string
  }
}

interface TicketDetailsProps {
  ticketId: string
  userId: string
  userRole: string
}

export function TicketDetails({ ticketId, userId, userRole }: TicketDetailsProps) {
  const router = useRouter()
  const [ticket, setTicket] = useState<Ticket | null>(null)
  const [replies, setReplies] = useState<TicketReply[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [replyMessage, setReplyMessage] = useState('')
  const [sendingReply, setSendingReply] = useState(false)
  const [updatingStatus, setUpdatingStatus] = useState(false)
  const [canEmployeeReply, setCanEmployeeReply] = useState(true)

  useEffect(() => {
    fetchTicketDetails()
  }, [ticketId])

  const fetchTicketDetails = async () => {
    const timeoutId = setTimeout(() => {
      console.warn('Ticket details fetch timeout - stopping loading')
      setLoading(false)
      setError('انتهت مهلة تحميل تفاصيل الطلب')
    }, 10000) // 10 second timeout

    try {
      setLoading(true)
      setError('')

      const result = await getTicketDetails(ticketId)

      clearTimeout(timeoutId)

      if (result.error) {
        console.error('Server action returned error:', result.error)
        setError(result.error)
        return
      }

      setTicket(result.ticket)
      setReplies(result.replies || [])
      setCanEmployeeReply(result.canEmployeeReply ?? true)
    } catch (error: any) {
      clearTimeout(timeoutId)
      console.error('Error fetching ticket details:', error)
      setError(`حدث خطأ في تحميل تفاصيل الطلب: ${error.message}`)
    } finally {
      setLoading(false)
    }
  }

  const handleSendReply = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!replyMessage.trim()) return

    try {
      setSendingReply(true)

      const result = await addTicketReply(ticketId, replyMessage.trim())

      if (result.error) {
        toast.error(result.error)
        return
      }

      setReplyMessage('')
      toast.success('تم إرسال الرد بنجاح')
      fetchTicketDetails() // Refresh to show new reply
    } catch (error: any) {
      console.error('Error sending reply:', error)
      toast.error('حدث خطأ في إرسال الرد')
    } finally {
      setSendingReply(false)
    }
  }

  const handleStatusChange = async (newStatus: 'open' | 'in_progress' | 'closed') => {
    if (!ticket) return

    try {
      setUpdatingStatus(true)

      const result = await updateTicketStatus(ticketId, newStatus)

      if (result.error) {
        toast.error(result.error)
        return
      }

      setTicket(prev => prev ? { ...prev, status: newStatus } : null)
      toast.success('تم تحديث حالة الطلب بنجاح')
    } catch (error: any) {
      console.error('Error updating ticket status:', error)
      toast.error('حدث خطأ في تحديث حالة الطلب')
    } finally {
      setUpdatingStatus(false)
    }
  }

  const handleBack = () => {
    router.push('/dashboard/tickets')
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">جاري تحميل تفاصيل الطلب...</p>
        </div>
      </div>
    )
  }

  if (error || !ticket) {
    return (
      <div className="text-center py-8">
        <p className="text-destructive mb-4">{error || 'لم يتم العثور على الطلب'}</p>
        <Button onClick={handleBack} variant="outline">
          العودة للطلبات
        </Button>
      </div>
    )
  }

  // Permission logic based on new hierarchy
  const canUpdateStatus = (() => {
    // System admins can edit all tickets
    if (userRole === 'system_admin') return true

    // Users cannot edit their own tickets (except system admin)
    if (ticket.created_by === userId) return false

    // Higher roles can edit tickets from lower roles
    if (userRole === 'area_manager') return true
    if (userRole === 'team_manager') return true

    return false
  })()

  const canReply = true // RLS policies will handle this

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-2">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleBack}
          className="flex items-center gap-2"
        >
          <ArrowRight className="h-4 w-4" />
          العودة للطلبات
        </Button>
      </div>

      {/* Ticket Details */}
      <Card className="border-l-4 border-l-primary/30">
        <CardHeader className="pb-4">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <CardTitle className="text-2xl mb-3 text-foreground">{ticket.title}</CardTitle>
              <CardDescription className="text-base leading-relaxed">
                {ticket.description}
              </CardDescription>
            </div>
            <div className="flex flex-col gap-3 mr-4 items-end">
              <TicketStatusBadge status={ticket.status} />
              <TicketPriorityBadge priority={ticket.priority} />
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between text-sm text-muted-foreground">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <UserAvatar
                  name={ticket.creator?.full_name || ticket.creator?.email || 'موظف مبيعات'}
                  className="h-6 w-6"
                />
                {ticket.creator ? (
                  <Link
                    href={`/dashboard/profile/${ticket.creator.id}`}
                    className="text-primary hover:underline"
                  >
                    {ticket.creator.full_name || ticket.creator.email}
                  </Link>
                ) : (
                  <span>موظف مبيعات</span>
                )}
              </div>
              {ticket.assignee && (
                <div className="flex items-center gap-2">
                  <span>مُعيَّن إلى:</span>
                  <UserAvatar
                    name={ticket.assignee.full_name || ticket.assignee.email}
                    className="h-6 w-6"
                  />
                  <Link
                    href={`/dashboard/profile/${ticket.assignee.id}`}
                    className="text-primary hover:underline"
                  >
                    {ticket.assignee.full_name || ticket.assignee.email}
                  </Link>
                </div>
              )}
            </div>
            <div className="flex items-center gap-1">
              <Clock className="h-4 w-4" />
              <span>
                {formatDistanceToNow(new Date(ticket.created_at), { 
                  addSuffix: true, 
                  locale: ar 
                })}
              </span>
            </div>
          </div>

          {/* Status Update (Team Managers only) */}
          {canUpdateStatus && (
            <div className="mt-6 pt-4 border-t">
              <div className="space-y-3">
                <Label className="text-sm font-medium">تحديث حالة الطلب:</Label>
                <div className="flex items-center gap-4" dir="rtl">
                  <div className="relative">
                    <Select
                      value={ticket.status}
                      onValueChange={handleStatusChange}
                      disabled={updatingStatus}
                      dir="rtl"
                    >
                      <SelectTrigger className="w-56 status-select-trigger text-right">
                        <SelectValue className="text-right">
                          <div className="flex items-center gap-2 w-full" dir="rtl">
                            <span className="flex-1 text-right">
                              {ticket.status === 'open' ? 'مفتوح' :
                               ticket.status === 'in_progress' ? 'قيد المعالجة' :
                               'مغلق'}
                            </span>
                            <div className="flex-shrink-0">
                              {ticket.status === 'open' && <AlertCircle className="h-4 w-4 text-red-600" />}
                              {ticket.status === 'in_progress' && <PlayCircle className="h-4 w-4 text-blue-600" />}
                              {ticket.status === 'closed' && <CheckCircle className="h-4 w-4 text-green-600" />}
                            </div>
                          </div>
                        </SelectValue>
                      </SelectTrigger>
                      <SelectContent className="bg-white border-2 border-primary/20 shadow-xl rounded-lg" dir="rtl">
                        <SelectItem value="open" className="status-select-item cursor-pointer hover:bg-sidebar-accent hover:text-white focus:bg-sidebar-accent focus:text-white transition-colors">
                          <div className="flex items-center gap-2 w-full" dir="rtl">
                            <span className="flex-1 text-right">مفتوح</span>
                            <div className="flex-shrink-0">
                              <AlertCircle className="h-4 w-4 text-red-600" />
                            </div>
                          </div>
                        </SelectItem>
                        <SelectItem value="in_progress" className="status-select-item cursor-pointer hover:bg-sidebar-accent hover:text-white focus:bg-sidebar-accent focus:text-white transition-colors">
                          <div className="flex items-center gap-2 w-full" dir="rtl">
                            <span className="flex-1 text-right">قيد المعالجة</span>
                            <div className="flex-shrink-0">
                              <PlayCircle className="h-4 w-4 text-blue-600" />
                            </div>
                          </div>
                        </SelectItem>
                        <SelectItem value="closed" className="status-select-item cursor-pointer hover:bg-sidebar-accent hover:text-white focus:bg-sidebar-accent focus:text-white transition-colors">
                          <div className="flex items-center gap-2 w-full" dir="rtl">
                            <span className="flex-1 text-right">مغلق</span>
                            <div className="flex-shrink-0">
                              <CheckCircle className="h-4 w-4 text-green-600" />
                            </div>
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                    {updatingStatus && (
                      <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                        <Loader2 className="h-4 w-4 animate-spin text-primary" />
                      </div>
                    )}
                  </div>
                  {updatingStatus && (
                    <p className="text-sm text-muted-foreground flex items-center gap-2">
                      جاري تحديث حالة الطلب...
                    </p>
                  )}
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Replies */}
      {replies.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>الردود ({replies.length})</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {replies.map((reply, index) => (
              <div key={reply.id}>
                <div className="flex items-start gap-3">
                  <UserAvatar
                    name={reply.user?.full_name || reply.user?.email || 'مستخدم'}
                    className="h-8 w-8 mt-1"
                  />
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      {reply.user ? (
                        <Link
                          href={`/dashboard/profile/${reply.user.id}`}
                          className="font-medium text-sm text-primary hover:underline"
                        >
                          {reply.user.full_name || reply.user.email}
                        </Link>
                      ) : (
                        <span className="font-medium text-sm">مستخدم</span>
                      )}
                      <span className="text-xs text-muted-foreground">
                        {formatDistanceToNow(new Date(reply.created_at), {
                          addSuffix: true,
                          locale: ar
                        })}
                      </span>
                    </div>
                    <p className="text-sm whitespace-pre-wrap">{reply.message}</p>
                  </div>
                </div>
                {index < replies.length - 1 && <Separator className="mt-4" />}
              </div>
            ))}
          </CardContent>
        </Card>
      )}

      {/* Reply Form */}
      {ticket.status !== 'closed' && canReply && (
        <Card>
          <CardHeader>
            <CardTitle>إضافة رد</CardTitle>
            {ticket.created_by === userId && !canEmployeeReply && (
              <CardDescription className="text-amber-600">
                {ticket.status === 'closed'
                  ? 'لا يمكن الرد على الطلبات المغلقة'
                  : 'يمكنك الرد فقط بعد أن يرد أحد المسؤولين على طلبك'
                }
              </CardDescription>
            )}
          </CardHeader>
          <CardContent>
            {canEmployeeReply ? (
              <form onSubmit={handleSendReply} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="reply">رسالتك</Label>
                  <Textarea
                    id="reply"
                    placeholder="اكتب ردك هنا..."
                    value={replyMessage}
                    onChange={(e) => setReplyMessage(e.target.value)}
                    disabled={sendingReply}
                    rows={4}
                    className="text-right resize-none"
                  />
                </div>
                <Button
                  type="submit"
                  disabled={sendingReply || !replyMessage.trim()}
                  className="flex items-center gap-2"
                >
                  {sendingReply ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      جاري الإرسال...
                    </>
                  ) : (
                    <>
                      <Send className="h-4 w-4" />
                      إرسال الرد
                    </>
                  )}
                </Button>
              </form>
            ) : (
              <div className="text-center py-8">
                <p className="text-muted-foreground">
                  {ticket.status === 'closed'
                    ? 'لا يمكن الرد على الطلبات المغلقة'
                    : 'انتظر رد أحد المسؤولين قبل أن تتمكن من الرد'
                  }
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  )
}
