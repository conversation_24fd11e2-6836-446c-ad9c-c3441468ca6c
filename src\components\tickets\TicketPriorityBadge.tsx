'use client'

import { Badge } from '@/components/ui/badge'
import { Alert<PERSON>riangle, Minus, ArrowUp } from 'lucide-react'
import { cn } from '@/lib/utils'

interface TicketPriorityBadgeProps {
  priority: 'low' | 'medium' | 'high'
  className?: string
  showIcon?: boolean
}

export function TicketPriorityBadge({ priority, className, showIcon = true }: TicketPriorityBadgeProps) {
  const getPriorityConfig = (priority: string) => {
    switch (priority) {
      case 'high':
        return {
          label: 'عالية',
          variant: 'destructive' as const,
          icon: AlertTriangle,
          bgColor: 'bg-orange-100 dark:bg-orange-950/30',
          textColor: 'text-orange-800 dark:text-orange-300',
          borderColor: 'border-orange-300 dark:border-orange-700',
        }
      case 'medium':
        return {
          label: 'متوسطة',
          variant: 'default' as const,
          icon: ArrowUp,
          bgColor: 'bg-amber-100 dark:bg-amber-950/30',
          textColor: 'text-amber-800 dark:text-amber-300',
          borderColor: 'border-amber-300 dark:border-amber-700',
        }
      case 'low':
        return {
          label: 'منخفضة',
          variant: 'secondary' as const,
          icon: Minus,
          bgColor: 'bg-slate-100 dark:bg-slate-950/30',
          textColor: 'text-slate-700 dark:text-slate-300',
          borderColor: 'border-slate-300 dark:border-slate-600',
        }
      default:
        return {
          label: 'غير معروف',
          variant: 'outline' as const,
          icon: Minus,
          bgColor: 'bg-gray-50 dark:bg-gray-950/20',
          textColor: 'text-gray-600 dark:text-gray-400',
          borderColor: 'border-gray-200 dark:border-gray-700',
        }
    }
  }

  const config = getPriorityConfig(priority)
  const IconComponent = config.icon

  return (
    <Badge
      variant={config.variant}
      className={cn(
        'flex items-center gap-1.5 px-2.5 py-1 text-xs font-medium rounded-full border transition-all duration-200',
        config.bgColor,
        config.textColor,
        config.borderColor,
        'hover:shadow-sm',
        className
      )}
    >
      {showIcon && <IconComponent className="h-3 w-3" />}
      {config.label}
    </Badge>
  )
}
