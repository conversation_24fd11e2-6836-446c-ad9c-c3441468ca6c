'use client'

import { Badge } from '@/components/ui/badge'
import { Circle, Clock, CheckCircle } from 'lucide-react'
import { cn } from '@/lib/utils'

interface TicketStatusBadgeProps {
  status: 'open' | 'in_progress' | 'closed'
  className?: string
  showIcon?: boolean
}

export function TicketStatusBadge({ status, className, showIcon = true }: TicketStatusBadgeProps) {
  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'open':
        return {
          label: 'مفتوح',
          variant: 'destructive' as const,
          icon: Circle,
          bgColor: 'bg-red-100 dark:bg-red-950/30',
          textColor: 'text-red-800 dark:text-red-300',
          borderColor: 'border-red-300 dark:border-red-700',
        }
      case 'in_progress':
        return {
          label: 'قيد المعالجة',
          variant: 'default' as const,
          icon: Clock,
          bgColor: 'bg-blue-100 dark:bg-blue-950/30',
          textColor: 'text-blue-800 dark:text-blue-300',
          borderColor: 'border-blue-300 dark:border-blue-700',
        }
      case 'closed':
        return {
          label: 'مغلق',
          variant: 'secondary' as const,
          icon: CheckCircle,
          bgColor: 'bg-green-100 dark:bg-green-950/30',
          textColor: 'text-green-800 dark:text-green-300',
          borderColor: 'border-green-300 dark:border-green-700',
        }
      default:
        return {
          label: 'غير معروف',
          variant: 'outline' as const,
          icon: Circle,
          bgColor: 'bg-gray-50 dark:bg-gray-950/20',
          textColor: 'text-gray-700 dark:text-gray-400',
          borderColor: 'border-gray-200 dark:border-gray-800',
        }
    }
  }

  const config = getStatusConfig(status)
  const IconComponent = config.icon

  return (
    <Badge
      variant={config.variant}
      className={cn(
        'flex items-center gap-1.5 px-2.5 py-1 text-xs font-medium rounded-full border transition-all duration-200',
        config.bgColor,
        config.textColor,
        config.borderColor,
        'hover:shadow-sm',
        className
      )}
    >
      {showIcon && <IconComponent className="h-3 w-3" />}
      {config.label}
    </Badge>
  )
}
