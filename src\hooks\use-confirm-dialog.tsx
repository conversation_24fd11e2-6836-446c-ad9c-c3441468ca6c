"use client"

import React, { useState, useCallback } from 'react'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog'

interface ConfirmDialogOptions {
  title?: string
  description?: string
  confirmText?: string
  cancelText?: string
  variant?: 'default' | 'destructive'
}

interface ConfirmDialogState {
  isOpen: boolean
  title: string
  description: string
  confirmText: string
  cancelText: string
  variant: 'default' | 'destructive'
  onConfirm: () => void
  onCancel: () => void
}

export function useConfirmDialog() {
  const [state, setState] = useState<ConfirmDialogState>({
    isOpen: false,
    title: '',
    description: '',
    confirmText: 'تأكيد',
    cancelText: 'إلغاء',
    variant: 'default',
    onConfirm: () => {},
    onCancel: () => {},
  })

  const confirm = useCallback((
    message: string,
    options: ConfirmDialogOptions = {}
  ): Promise<boolean> => {
    return new Promise((resolve) => {
      setState({
        isOpen: true,
        title: options.title || 'تأكيد العملية',
        description: message,
        confirmText: options.confirmText || 'تأكيد',
        cancelText: options.cancelText || 'إلغاء',
        variant: options.variant || 'default',
        onConfirm: () => {
          setState(prev => ({ ...prev, isOpen: false }))
          resolve(true)
        },
        onCancel: () => {
          setState(prev => ({ ...prev, isOpen: false }))
          resolve(false)
        },
      })
    })
  }, [])

  const ConfirmDialog = useCallback(() => (
    <AlertDialog open={state.isOpen} onOpenChange={(open) => {
      if (!open) {
        state.onCancel()
      }
    }}>
      <AlertDialogContent className="max-w-md" dir="rtl">
        <AlertDialogHeader>
          <AlertDialogTitle className="text-right">
            {state.title}
          </AlertDialogTitle>
          <AlertDialogDescription className="text-right">
            {state.description}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter className="flex-row-reverse gap-2">
          <AlertDialogAction
            onClick={state.onConfirm}
            className={state.variant === 'destructive' ? 'bg-destructive hover:bg-destructive/90' : ''}
          >
            {state.confirmText}
          </AlertDialogAction>
          <AlertDialogCancel onClick={state.onCancel}>
            {state.cancelText}
          </AlertDialogCancel>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  ), [state])

  return { confirm, ConfirmDialog }
}
