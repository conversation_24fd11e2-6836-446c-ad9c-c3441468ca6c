-- Fix team manager ticket access
-- Allow team managers to create tickets

-- Drop existing policies that might conflict
DROP POLICY IF EXISTS "Team managers can create tickets" ON public.tickets;

-- Team managers can create their own tickets
CREATE POLICY "Team managers can create tickets" ON public.tickets
    FOR INSERT WITH CHECK (
        created_by = auth.uid() AND
        EXISTS (
            SELECT 1 FROM public.profiles
            WHERE id = auth.uid() AND role = 'team_manager'
        )
    );

-- Update the existing team manager view policy to include tickets they created
DROP POLICY IF EXISTS "Team managers can view team tickets" ON public.tickets;

CREATE POLICY "Team managers can view team tickets" ON public.tickets
    FOR SELECT USING (
        -- Team managers can see their own tickets
        (created_by = auth.uid() AND EXISTS (
            SELECT 1 FROM public.profiles
            WHERE id = auth.uid() AND role = 'team_manager'
        ))
        OR
        -- Team managers can see tickets from their team members
        EXISTS (
            SELECT 1 FROM public.profiles p
            JOIN public.teams t ON p.team_id = t.id
            WHERE p.id = tickets.created_by 
            AND t.manager_id = auth.uid()
            AND EXISTS (
                SELECT 1 FROM public.profiles pm
                WHERE pm.id = auth.uid() AND pm.role = 'team_manager'
            )
        )
    );

-- Update the team manager update policy to include their own tickets
DROP POLICY IF EXISTS "Team managers can update team tickets" ON public.tickets;

CREATE POLICY "Team managers can update team tickets" ON public.tickets
    FOR UPDATE USING (
        -- Team managers can update their own tickets
        (created_by = auth.uid() AND EXISTS (
            SELECT 1 FROM public.profiles
            WHERE id = auth.uid() AND role = 'team_manager'
        ))
        OR
        -- Team managers can update tickets from their team members
        EXISTS (
            SELECT 1 FROM public.profiles p
            JOIN public.teams t ON p.team_id = t.id
            WHERE p.id = tickets.created_by 
            AND t.manager_id = auth.uid()
            AND EXISTS (
                SELECT 1 FROM public.profiles pm
                WHERE pm.id = auth.uid() AND pm.role = 'team_manager'
            )
        )
    );
