-- Complete Ticket System Rework
-- Establish proper role hierarchy and permissions

-- Drop all existing ticket policies and triggers
DROP TRIGGER IF EXISTS assign_ticket_to_team_manager_trigger ON public.tickets;
DROP FUNCTION IF EXISTS public.assign_ticket_to_team_manager();

DROP POLICY IF EXISTS "Sales employees can create tickets" ON public.tickets;
DROP POLICY IF EXISTS "Team managers can create tickets" ON public.tickets;
DROP POLICY IF EXISTS "Sales employees can view own tickets" ON public.tickets;
DROP POLICY IF EXISTS "Team managers can view team tickets" ON public.tickets;
DROP POLICY IF EXISTS "Area managers can view area tickets" ON public.tickets;
DROP POLICY IF EXISTS "System admins can view all tickets" ON public.tickets;
DROP POLICY IF EXISTS "Team managers can update team tickets" ON public.tickets;
DROP POLICY IF EXISTS "Area managers can update area tickets" ON public.tickets;
DROP POLICY IF EXISTS "System admins can update all tickets" ON public.tickets;

-- <PERSON><PERSON> helper function to get user's role level
CREATE OR REPLACE FUNCTION public.get_user_role_level(user_id uuid)
R<PERSON>URNS integer AS $$
DECLARE
    user_role text;
BEGIN
    SELECT role INTO user_role FROM public.profiles WHERE id = user_id;

    CASE user_role
        WHEN 'system_admin' THEN RETURN 1;
        WHEN 'area_manager' THEN RETURN 2;
        WHEN 'team_manager' THEN RETURN 3;
        WHEN 'sales_employee' THEN RETURN 4;
        ELSE RETURN 999;
    END CASE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to check if user can view ticket
CREATE OR REPLACE FUNCTION public.can_user_view_ticket(user_id uuid, ticket_creator_id uuid)
RETURNS boolean AS $$
DECLARE
    user_role text;
    user_area_id uuid;
    user_team_id uuid;
    creator_area_id uuid;
    creator_team_id uuid;
    managed_team_id uuid;
BEGIN
    -- Get user profile
    SELECT role, area_id, team_id INTO user_role, user_area_id, user_team_id
    FROM public.profiles WHERE id = user_id;

    -- Get creator profile
    SELECT area_id, team_id INTO creator_area_id, creator_team_id
    FROM public.profiles WHERE id = ticket_creator_id;

    CASE user_role
        WHEN 'system_admin' THEN
            RETURN true; -- Can see all tickets

        WHEN 'area_manager' THEN
            -- Can see tickets from their area
            RETURN creator_area_id = user_area_id;

        WHEN 'team_manager' THEN
            -- Can see tickets from team members they manage
            SELECT id INTO managed_team_id FROM public.teams WHERE manager_id = user_id;
            RETURN creator_team_id = managed_team_id;

        WHEN 'sales_employee' THEN
            -- Can only see their own tickets
            RETURN user_id = ticket_creator_id;

        ELSE
            RETURN false;
    END CASE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to check if user can edit ticket
CREATE OR REPLACE FUNCTION public.can_user_edit_ticket(user_id uuid, ticket_creator_id uuid)
RETURNS boolean AS $$
DECLARE
    user_role text;
    user_area_id uuid;
    creator_area_id uuid;
    creator_team_id uuid;
    managed_team_id uuid;
BEGIN
    -- Users cannot edit their own tickets (except system admin)
    IF user_id = ticket_creator_id THEN
        SELECT role INTO user_role FROM public.profiles WHERE id = user_id;
        RETURN user_role = 'system_admin';
    END IF;

    -- Get user profile
    SELECT role, area_id INTO user_role, user_area_id
    FROM public.profiles WHERE id = user_id;

    -- Get creator profile
    SELECT area_id, team_id INTO creator_area_id, creator_team_id
    FROM public.profiles WHERE id = ticket_creator_id;

    CASE user_role
        WHEN 'system_admin' THEN
            RETURN true; -- Can edit all tickets

        WHEN 'area_manager' THEN
            -- Can edit tickets from their area (but not their own)
            RETURN creator_area_id = user_area_id;

        WHEN 'team_manager' THEN
            -- Can edit tickets from team members they manage
            SELECT id INTO managed_team_id FROM public.teams WHERE manager_id = user_id;
            RETURN creator_team_id = managed_team_id;

        ELSE
            RETURN false; -- Sales employees cannot edit any tickets
    END CASE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create comprehensive ticket assignment function
CREATE OR REPLACE FUNCTION public.assign_ticket_hierarchically()
RETURNS TRIGGER AS $$
DECLARE
    creator_role text;
    creator_area_id uuid;
    creator_team_id uuid;
    assignee_id uuid;
BEGIN
    -- Get creator's profile
    SELECT role, area_id, team_id INTO creator_role, creator_area_id, creator_team_id
    FROM public.profiles WHERE id = NEW.created_by;

    CASE creator_role
        WHEN 'sales_employee' THEN
            -- Assign to team manager
            SELECT manager_id INTO assignee_id
            FROM public.teams WHERE id = creator_team_id;

        WHEN 'team_manager' THEN
            -- Assign to area manager (find the area manager for the team manager's area)
            SELECT a.manager_id INTO assignee_id
            FROM public.teams t
            JOIN public.areas a ON t.area_id = a.id
            WHERE t.manager_id = NEW.created_by;

        WHEN 'area_manager' THEN
            -- Assign to system admin (find any system admin)
            SELECT id INTO assignee_id
            FROM public.profiles
            WHERE role = 'system_admin'
            LIMIT 1;

        WHEN 'system_admin' THEN
            -- Self-assign
            assignee_id := NEW.created_by;
    END CASE;

    -- Update the ticket with assignment
    IF assignee_id IS NOT NULL THEN
        NEW.assigned_to := assignee_id;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create the assignment trigger
CREATE TRIGGER assign_ticket_hierarchically_trigger
    BEFORE INSERT ON public.tickets
    FOR EACH ROW
    EXECUTE FUNCTION public.assign_ticket_hierarchically();

-- Create comprehensive RLS policies

-- INSERT policy: All authenticated users can create tickets
CREATE POLICY "Authenticated users can create tickets" ON public.tickets
    FOR INSERT WITH CHECK (
        auth.uid() = created_by AND
        EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid())
    );

-- SELECT policy: Role-based ticket visibility
CREATE POLICY "Role-based ticket visibility" ON public.tickets
    FOR SELECT USING (
        public.can_user_view_ticket(auth.uid(), created_by)
    );

-- UPDATE policy: Role-based ticket editing
CREATE POLICY "Role-based ticket editing" ON public.tickets
    FOR UPDATE USING (
        public.can_user_edit_ticket(auth.uid(), created_by)
    );

-- DELETE policy: Only system admins can delete tickets
CREATE POLICY "System admins can delete tickets" ON public.tickets
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM public.profiles
            WHERE id = auth.uid() AND role = 'system_admin'
        )
    );

-- Ticket replies policies
DROP POLICY IF EXISTS "Users can view ticket replies" ON public.ticket_replies;
DROP POLICY IF EXISTS "Users can create ticket replies" ON public.ticket_replies;
DROP POLICY IF EXISTS "Users can update ticket replies" ON public.ticket_replies;

-- Replies SELECT: Can view replies for tickets they can view
CREATE POLICY "Role-based reply visibility" ON public.ticket_replies
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.tickets t
            WHERE t.id = ticket_replies.ticket_id
            AND public.can_user_view_ticket(auth.uid(), t.created_by)
        )
    );

-- Replies INSERT: Can reply to tickets they can view
CREATE POLICY "Role-based reply creation" ON public.ticket_replies
    FOR INSERT WITH CHECK (
        auth.uid() = user_id AND
        EXISTS (
            SELECT 1 FROM public.tickets t
            WHERE t.id = ticket_replies.ticket_id
            AND public.can_user_view_ticket(auth.uid(), t.created_by)
        )
    );
