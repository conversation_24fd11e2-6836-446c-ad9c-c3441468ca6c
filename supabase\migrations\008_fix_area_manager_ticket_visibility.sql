-- Fix area manager ticket visibility
-- Ensure all users have proper area_id assignments and improve ticket visibility

-- First, ensure all team members have the correct area_id based on their team
UPDATE public.profiles 
SET area_id = teams.area_id
FROM public.teams 
WHERE profiles.team_id = teams.id 
AND profiles.area_id IS NULL;

-- Update the ticket visibility function to be more explicit about area manager access
CREATE OR REPLACE FUNCTION public.can_user_view_ticket(user_id uuid, ticket_creator_id uuid)
RETURNS boolean AS $$
DECLARE
    user_role text;
    user_area_id uuid;
    user_team_id uuid;
    creator_area_id uuid;
    creator_team_id uuid;
    managed_team_id uuid;
BEGIN
    -- Get user profile
    SELECT role, area_id, team_id INTO user_role, user_area_id, user_team_id
    FROM public.profiles WHERE id = user_id;
    
    -- Get creator profile
    SELECT area_id, team_id INTO creator_area_id, creator_team_id
    FROM public.profiles WHERE id = ticket_creator_id;
    
    CASE user_role
        WHEN 'system_admin' THEN
            RETURN true; -- Can see all tickets
            
        WHEN 'area_manager' THEN
            -- Area managers can see ALL tickets from their area
            -- This includes tickets from:
            -- 1. Sales employees in their area
            -- 2. Team managers in their area  
            -- 3. Anyone else assigned to their area
            RETURN creator_area_id = user_area_id;
            
        WHEN 'team_manager' THEN
            -- Team managers can see tickets from team members they manage
            SELECT id INTO managed_team_id FROM public.teams WHERE manager_id = user_id;
            RETURN creator_team_id = managed_team_id;
            
        WHEN 'sales_employee' THEN
            -- Sales employees can only see their own tickets
            RETURN user_id = ticket_creator_id;
            
        ELSE
            RETURN false;
    END CASE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Also update the edit function to ensure area managers can edit tickets from their area
CREATE OR REPLACE FUNCTION public.can_user_edit_ticket(user_id uuid, ticket_creator_id uuid)
RETURNS boolean AS $$
DECLARE
    user_role text;
    user_area_id uuid;
    creator_area_id uuid;
    creator_team_id uuid;
    managed_team_id uuid;
BEGIN
    -- Users cannot edit their own tickets (except system admin)
    IF user_id = ticket_creator_id THEN
        SELECT role INTO user_role FROM public.profiles WHERE id = user_id;
        RETURN user_role = 'system_admin';
    END IF;
    
    -- Get user profile
    SELECT role, area_id INTO user_role, user_area_id
    FROM public.profiles WHERE id = user_id;
    
    -- Get creator profile
    SELECT area_id, team_id INTO creator_area_id, creator_team_id
    FROM public.profiles WHERE id = ticket_creator_id;
    
    CASE user_role
        WHEN 'system_admin' THEN
            RETURN true; -- Can edit all tickets
            
        WHEN 'area_manager' THEN
            -- Area managers can edit ALL tickets from their area
            -- This includes tickets from sales employees and team managers
            RETURN creator_area_id = user_area_id;
            
        WHEN 'team_manager' THEN
            -- Team managers can edit tickets from team members they manage
            SELECT id INTO managed_team_id FROM public.teams WHERE manager_id = user_id;
            RETURN creator_team_id = managed_team_id;
            
        ELSE
            RETURN false; -- Sales employees cannot edit any tickets
    END CASE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
